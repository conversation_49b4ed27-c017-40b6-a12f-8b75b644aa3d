'use client';

import Image from 'next/image';
import Link from 'next/link';
import { MoveRight, X } from 'lucide-react';
import { useState } from 'react';

// Feature data
const features = [
  {
    id: 1,
    title: "Context-aware code intelligence system",
    image: "/images/cubent-feature-1.png",
    alt: "Context Intelligence",
    description: "<PERSON><PERSON><PERSON> understands your entire codebase context, making intelligent suggestions that align with your project's architecture and patterns.",
    content: [
      "At the heart of modern software development lies the challenge of maintaining context across vast codebases. <PERSON><PERSON><PERSON> revolutionizes this experience by providing deep, contextual understanding of your entire project ecosystem. Our advanced AI doesn't just read your code—it comprehends the intricate relationships between components, understands your architectural decisions, and learns from your coding patterns.",

      "Unlike traditional code assistants that work in isolation, <PERSON><PERSON><PERSON> maintains a comprehensive map of your project's structure, dependencies, and design patterns. This enables it to make suggestions that aren't just syntactically correct, but architecturally sound and consistent with your existing codebase. Whether you're working on a microservices architecture, a monolithic application, or a complex distributed system, <PERSON><PERSON><PERSON> adapts to your specific context.",

      "The intelligence extends beyond simple code completion. <PERSON><PERSON><PERSON> analyzes cross-file dependencies, understands the impact of changes across your entire system, and can predict potential issues before they arise. This proactive approach to development helps teams maintain code quality while accelerating their development velocity, making it an indispensable tool for serious product development teams."
    ]
  },
  {
    id: 2,
    title: "Screenshot to code in seconds plus",
    image: "/images/cubent-feature-2.png",
    alt: "AI Screenshot Analysis",
    description: "Transform screenshots and designs into working code instantly. Cubent analyzes visual elements and generates pixel-perfect implementations.",
    content: [
      "In today's fast-paced development environment, the ability to rapidly prototype and implement designs is crucial for staying competitive. Cubent's revolutionary screenshot-to-code technology bridges the gap between design and implementation, allowing developers to transform visual mockups into functional code in seconds rather than hours.",

      "Our advanced computer vision algorithms analyze every pixel of your designs, understanding not just what elements are present, but how they should behave and interact. The system recognizes common UI patterns, understands responsive design principles, and generates code that follows modern best practices. Whether you're working with Figma designs, hand-drawn sketches, or competitor screenshots, Cubent can interpret and implement them accurately.",

      "The generated code isn't just a static representation—it's production-ready, accessible, and optimized for performance. Cubent automatically handles responsive breakpoints, generates semantic HTML, applies appropriate ARIA labels, and ensures cross-browser compatibility. This means you can go from concept to working prototype in minutes, allowing for rapid iteration and faster time-to-market for your products."
    ]
  },
  {
    id: 3,
    title: "Deep codebase understanding engine",
    image: "/images/cubent-feature-3.png",
    alt: "Smart Code Editing",
    description: "Experience intelligent code editing that understands your intent. Cubent provides contextual suggestions and automated improvements.",
    content: [
      "Perfect code isn't just about functionality—it's about maintainability, performance, and elegance. Cubent's intelligent editing capabilities go far beyond traditional autocomplete, offering a sophisticated understanding of code quality, performance implications, and best practices. Every suggestion is crafted with the goal of not just making your code work, but making it exceptional.",

      "The system continuously analyzes your code for potential improvements, from micro-optimizations that enhance performance to architectural suggestions that improve maintainability. Cubent understands the nuances of different programming languages, frameworks, and design patterns, allowing it to provide highly specific and relevant recommendations tailored to your technology stack and coding style.",

      "What sets Cubent apart is its ability to learn and adapt to your team's specific standards and preferences. It recognizes your coding conventions, understands your project's unique requirements, and evolves its suggestions to match your team's definition of perfect code. This results in a more consistent codebase, reduced technical debt, and a development experience that feels truly personalized and intelligent."
    ]
  }
];

export const Mockup = () => {
  const [selectedFeature, setSelectedFeature] = useState<typeof features[0] | null>(null);

  return (
  <div className="w-full relative">
    {/* Grid background for GIF section */}
    <div
      className="absolute inset-0 opacity-20"
      style={{
        backgroundImage: `
          linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
          linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
        `,
        backgroundSize: '40px 40px'
      }}
    />
    <div className="container mx-auto px-2 sm:px-4 lg:px-6 relative z-10">
      <div className="flex flex-col items-center justify-center gap-2 py-4">
        <div className="relative w-full max-w-7xl">
          <div className="relative overflow-hidden">
            <Image
              src="/images/Cubent.Dev.gif"
              alt="Cubent Editor Interface - Code editing with AI assistance"
              width={1200}
              height={800}
              className="w-full h-auto object-cover rounded-lg"
              priority
              unoptimized
            />
            {/* Soft glow effect */}
            <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 rounded-2xl blur-xl opacity-30 -z-10" />
          </div>
        </div>
      </div>
    </div>

    {/* Made for modern product teams section */}
    <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
      {/* Grid background */}
      <div
        className="absolute inset-0 opacity-20"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px'
        }}
      />
      {/* Top section - Title on left, description on right */}
      <div className="relative z-10 flex flex-col lg:flex-row gap-8 lg:gap-16 items-start mb-16 lg:mb-20 max-w-5xl mx-auto">
        {/* Left side - Title */}
        <div className="flex-1 max-w-md">
          <h2 className="text-4xl lg:text-5xl font-regular tracking-tighter text-white">
            AI-powered development that understands your code
          </h2>
        </div>

        {/* Right side - Description and link */}
        <div className="flex-1 max-w-lg">
          <p className="text-lg text-muted-foreground leading-relaxed mb-4">
            Cubent transforms how developers work by providing intelligent, context-aware assistance that learns from your codebase. From instant screenshot-to-code conversion to deep architectural understanding, we're building the future of software development.
          </p>
          <Link href="#" className="text-white hover:text-muted-foreground transition-colors inline-flex items-center gap-2">
            Make the switch <MoveRight className="h-4 w-4" />
          </Link>
        </div>
      </div>

      {/* Bottom section - Three feature cards in a row */}
      <div className="relative z-10 max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <div
              key={feature.id}
              onClick={() => setSelectedFeature(feature)}
              className={`group relative bg-[#1a1a1a] rounded-3xl overflow-hidden hover:bg-[#222222] transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-black/50 cursor-pointer ${
                index === 2 ? 'md:col-span-2 lg:col-span-1' : ''
              }`}
            >
              <div className="relative aspect-square w-full overflow-hidden">
                <Image
                  src={feature.image}
                  alt={feature.alt}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6 pr-16">
                <h3 className="text-xl font-medium text-white mb-2 leading-tight break-words">
                  {feature.title}
                </h3>
              </div>
              <div className="absolute bottom-5 right-5 w-12 h-12 border border-white/20 rounded-full flex items-center justify-center text-white/70 group-hover:bg-white/10 group-hover:text-white transition-all duration-300">
                <span className="text-2xl leading-none">+</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Split Screen Tools Section */}
      <div className="relative z-10 max-w-7xl mx-auto mt-32">
        {/* First Split Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-32">
          {/* Left Content */}
          <div className="space-y-6">
            <div className="text-sm text-blue-400 font-medium tracking-wide uppercase">
              Extension Tools
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
              Powerful MCP Integration
            </h2>
            <p className="text-lg text-gray-300 leading-relaxed">
              Built with Model Context Protocol, we've integrated the most advanced
              AI tools for developers who want to extend their coding capabilities.
              Seamless integration, powerful automation, and best-in-class extensibility.
            </p>
          </div>

          {/* Right Code Interface */}
          <div className="bg-[#0d1117] rounded-xl border border-gray-800 overflow-hidden">
            <div className="bg-[#161b22] px-4 py-3 border-b border-gray-800 flex items-center gap-2">
              <div className="flex gap-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <div className="text-gray-400 text-sm ml-4">MCP Tools</div>
            </div>
            <div className="p-6 font-mono text-sm relative">
              {/* Line numbers */}
              <div className="absolute left-4 top-6 space-y-1 text-gray-500 select-none">
                {Array.from({ length: 12 }, (_, i) => (
                  <div key={i + 1} className="leading-6">{i + 1}</div>
                ))}
              </div>
              {/* Code content */}
              <div className="ml-8 space-y-1 text-gray-300">
                <div><span className="text-purple-400">import</span> <span className="text-blue-400">{ MCPClient }</span> <span className="text-purple-400">from</span> <span className="text-green-400">'@cubent/mcp'</span></div>
                <div><span className="text-purple-400">import</span> <span className="text-blue-400">{ AIAgent }</span> <span className="text-purple-400">from</span> <span className="text-green-400">'@cubent/agents'</span></div>
                <div></div>
                <div><span className="text-purple-400">const</span> <span className="text-blue-400">client</span> = <span className="text-purple-400">new</span> <span className="text-yellow-400">MCPClient</span>({</div>
                <div className="ml-4"><span className="text-red-400">tools</span>: [<span className="text-green-400">'file-system'</span>, <span className="text-green-400">'git'</span>, <span className="text-green-400">'terminal'</span>],</div>
                <div className="ml-4"><span className="text-red-400">agents</span>: [<span className="text-green-400">'code-reviewer'</span>, <span className="text-green-400">'debugger'</span>]</div>
                <div>})</div>
                <div></div>
                <div><span className="text-purple-400">await</span> <span className="text-blue-400">client</span>.<span className="text-yellow-400">execute</span>(<span className="text-green-400">'analyze-codebase'</span>)</div>
                <div><span className="text-gray-500">// MCP Protocol Integration</span></div>
                <div><span className="text-gray-500">// Custom AI Agents</span></div>
                <div><span className="text-gray-500">// Real-time Tool Execution</span></div>
              </div>
            </div>
          </div>
        </div>

        {/* Second Split Section - Inverted */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Image Placeholder */}
          <div className="bg-[#1a1a1a] rounded-2xl aspect-[4/3] border border-gray-800 flex items-center justify-center lg:order-1">
            <div className="text-gray-500 text-center">
              <div className="text-4xl mb-2">🛠️</div>
              <div className="text-sm">Extension API Interface</div>
            </div>
          </div>

          {/* Right Content */}
          <div className="space-y-6 lg:order-2">
            <div className="text-sm text-green-400 font-medium tracking-wide uppercase">
              Developer API
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-white leading-tight">
              Extensible Architecture
            </h2>
            <p className="text-lg text-gray-300 leading-relaxed">
              Create custom extensions and integrate with any service. Our robust API
              framework supports unlimited extensibility with type-safe interfaces,
              hot-reloading, and seamless deployment.
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Modal */}
    {selectedFeature && (
      <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <div className="bg-[#1a1a1a] rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
          {/* Modal Header */}
          <div className="relative">
            <button
              onClick={() => setSelectedFeature(null)}
              className="absolute top-6 right-6 z-10 w-10 h-10 bg-black/20 hover:bg-black/40 rounded-full flex items-center justify-center text-white/70 hover:text-white transition-all duration-200"
            >
              <X size={20} />
            </button>

            {/* Feature Image */}
            <div className="relative h-80 w-full overflow-hidden">
              <Image
                src={selectedFeature.image}
                alt={selectedFeature.alt}
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-[#1a1a1a] via-transparent to-transparent" />
            </div>
          </div>

          {/* Modal Content */}
          <div className="p-8 max-h-[50vh] overflow-y-auto">
            <h2 className="text-3xl font-bold text-white mb-6">
              {selectedFeature.title}
            </h2>

            <p className="text-gray-300 text-lg mb-8 leading-relaxed">
              {selectedFeature.description}
            </p>

            <div className="space-y-6">
              {selectedFeature.content.map((paragraph, index) => (
                <p key={index} className="text-gray-300 leading-relaxed text-base">
                  {paragraph}
                </p>
              ))}
            </div>

            <div className="mt-8 pt-6 border-t border-white/10">
              <p className="text-gray-400 text-sm leading-relaxed">
                Experience the power of AI-driven development with Cubent's advanced features designed to accelerate your workflow and improve code quality. Join thousands of developers who have already transformed their development process with intelligent, context-aware coding assistance.
              </p>
            </div>
          </div>
        </div>
      </div>
    )}
  </div>
  );
};
