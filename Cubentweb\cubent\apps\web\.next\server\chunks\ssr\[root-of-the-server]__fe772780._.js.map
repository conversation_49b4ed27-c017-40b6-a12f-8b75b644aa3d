{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6VAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4IAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/animated-title.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AnimatedTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call AnimatedTitle() from the server but AnimatedTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/animated-title.tsx <module evaluation>\",\n    \"AnimatedTitle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,4FACA", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/animated-title.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AnimatedTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call AnimatedTitle() from the server but AnimatedTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/animated-title.tsx\",\n    \"AnimatedTitle\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,wEACA", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/hero.tsx"], "sourcesContent": ["import { env } from '@/env';\r\nimport { blog } from '@repo/cms';\r\nimport { Feed } from '@repo/cms/components/feed';\r\nimport { Button } from '@repo/design-system/components/ui/button';\r\nimport type { Dictionary } from '@repo/internationalization';\r\nimport { MoveRight, PhoneCall } from 'lucide-react';\r\nimport Link from 'next/link';\r\nimport { AnimatedTitle } from './animated-title';\r\n\r\ntype HeroProps = {\r\n  dictionary: Dictionary;\r\n};\r\n\r\n// Hero component for the homepage\r\nexport const Hero = async ({ dictionary }: HeroProps) => (\r\n  <div className=\"w-full relative overflow-hidden -mt-20 pt-20\">\r\n    {/* Static coding languages and symbols background */}\r\n    <div className=\"absolute inset-0 -top-20 pointer-events-none overflow-hidden opacity-15\">\r\n      {/* Programming Language Icons */}\r\n      <div className=\"absolute w-8 h-8 text-gray-400\" style={{ left: '5%', top: '15%' }}>\r\n        <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\r\n          <path d=\"m2 2h12v12h-12v-12m3.1533 10.027c0.26667 0.56667 0.79333 1.0333 1.6933 1.0333 1 0 1.6867-0.53333 1.6867-1.7v-3.8533h-1.1333v3.8267c0 0.57333-0.23333 0.72-0.6 0.72-0.38667 0-0.54667-0.26667-0.72667-0.58l-0.92 0.55333m3.9867-0.12c0.33333 0.65333 1.0067 1.1533 2.06 1.1533 1.0667 0 1.8667-0.55333 1.8667-1.5733 0-0.94-0.54-1.36-1.5-1.7733l-0.28-0.12c-0.48667-0.20667-0.69333-0.34667-0.69333-0.68 0-0.27333 0.20667-0.48667 0.54-0.48667 0.32 0 0.53333 0.14 0.72667 0.48667l0.87333-0.58c-0.36667-0.64-0.88667-0.88667-1.6-0.88667-1.0067 0-1.6533 0.64-1.6533 1.4867 0 0.92 0.54 1.3533 1.3533 1.7l0.28 0.12c0.52 0.22667 0.82667 0.36667 0.82667 0.75333 0 0.32-0.3 0.55333-0.76667 0.55333-0.55333 0-0.87333-0.28667-1.1133-0.68667z\" fill=\"currentColor\" strokeWidth=\".66667\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-6 h-6 text-gray-400\" style={{ left: '85%', top: '12%' }}>\r\n        <svg viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\r\n          <path d=\"M9.86 2A2.86 2.86 0 0 0 7 4.86v1.68h4.29c.39 0 .71.57.71.96H4.86A2.86 2.86 0 0 0 2 10.36v3.781a2.86 2.86 0 0 0 2.86 2.86h1.18v-2.68a2.85 2.85 0 0 1 2.85-2.86h5.25c1.58 0 2.86-1.271 2.86-2.851V4.86A2.86 2.86 0 0 0 14.14 2zm-.72 1.61c.4 0 .72.12.72.71s-.32.891-.72.891c-.39 0-.71-.3-.71-.89s.32-.711.71-.711z\" fill=\"currentColor\"/>\r\n          <path d=\"M17.959 7v2.68a2.85 2.85 0 0 1-2.85 2.859H9.86A2.85 2.85 0 0 0 7 15.389v3.75a2.86 2.86 0 0 0 2.86 2.86h4.28A2.86 2.86 0 0 0 17 19.14v-1.68h-4.291c-.39 0-.709-.57-.709-.96h7.14A2.86 2.86 0 0 0 22 13.64V9.86A2.86 2.86 0 0 0 19.14 7zM8.32 11.513l-.004.004c.012-.002.025-.001.038-.004zm6.54 7.276c.39 0 .71.3.71.89a.71.71 0 0 1-.71.71c-.4 0-.72-.12-.72-.71s.32-.89.72-.89z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-8 h-8 text-gray-400\" style={{ left: '15%', top: '25%' }}>\r\n        <svg width=\"16\" height=\"16\" viewBox=\"0 0 16 16\" xmlns=\"http://www.w3.org/2000/svg\" className=\"w-full h-full\">\r\n          <path d=\"m2 2h12v12h-12v-12m7.14 9.9067c0.33333 0.65333 1.0067 1.1533 2.06 1.1533 1.0667 0 1.8667-0.55333 1.8667-1.5733 0-0.94-0.54-1.36-1.5-1.7733l-0.28-0.12c-0.48667-0.20667-0.69333-0.34667-0.69333-0.68 0-0.27333 0.20667-0.48667 0.54-0.48667 0.32 0 0.53333 0.14 0.72667 0.48667l0.87333-0.58c-0.36667-0.64-0.88667-0.88667-1.6-0.88667-1.0067 0-1.6533 0.64-1.6533 1.4867 0 0.92 0.54 1.3533 1.3533 1.7l0.28 0.12c0.52 0.22667 0.82667 0.36667 0.82667 0.75333 0 0.32-0.3 0.55333-0.76667 0.55333-0.55333 0-0.87333-0.28667-1.1133-0.68667l-0.92 0.53333m-0.47333-4.4067h-3.3333v1h1v4.8333h1.1667v-4.8333h1.1667z\" fill=\"currentColor\" strokeWidth=\".66667\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-6 h-6 text-gray-400\" style={{ left: '75%', top: '28%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M16,12c7.44405,0,12,2.58981,12,4s-4.55595,4-12,4S4,17.41019,4,16,8.556,12,16,12m0-2C8.268,10,2,12.68629,2,16s6.268,6,14,6,14-2.68629,14-6-6.268-6-14-6Z\" fill=\"currentColor\"/>\r\n          <path d=\"M16,14a2,2,0,1,0,2,2,2,2,0,0,0-2-2Z\" fill=\"currentColor\"/>\r\n          <path d=\"M10.45764,5.50706C12.47472,5.50746,16.395,8.68416,19.4641,14,23.18613,20.44672,23.22125,25.68721,22,26.3923a.90009.90009,0,0,1-.45691.10064c-2.01725,0-5.93792-3.17678-9.00721-8.49294C8.81387,11.55328,8.77875,6.31279,10,5.6077a.90278.90278,0,0,1,.45766-.10064m-.00076-2A2.87113,2.87113,0,0,0,9,3.87564C6.13025,5.5325,6.93785,12.30391,10.80385,19c3.28459,5.68906,7.71948,9.49292,10.73927,9.49292A2.87033,2.87033,0,0,0,23,28.12436C25.86975,26.4675,25.06215,19.69609,21.19615,13c-3.28459-5.68906-7.71948-9.49342-10.73927-9.49292Z\" fill=\"currentColor\"/>\r\n          <path d=\"M21.54311,5.50706A.9.9,0,0,1,22,5.6077c1.22125.70509,1.18613,5.94558-2.5359,12.3923-3.06929,5.31616-6.99,8.49294-9.00721,8.49294A.9.9,0,0,1,10,26.3923C8.77875,25.68721,8.81387,20.44672,12.5359,14c3.06929-5.31616,6.99-8.49294,9.00721-8.49294m0-2c-3.01979,0-7.45468,3.80386-10.73927,9.49292C6.93785,19.69609,6.13025,26.4675,9,28.12436a2.87033,2.87033,0,0,0,1.45688.36856c3.01979,0,7.45468-3.80386,10.73927-9.49292C25.06215,12.30391,25.86975,5.5325,23,3.87564a2.87033,2.87033,0,0,0-1.45688-.36856Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-8 h-8 text-gray-400\" style={{ left: '8%', top: '35%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M16,30A14,14,0,1,1,30,16,14,14,0,0,1,16,30ZM16,4A12,12,0,1,0,28,16,12,12,0,0,0,16,4Z\" fill=\"currentColor\"/>\r\n          <path d=\"M16,24.414l-6.707-6.707,1.414-1.414L16,21.586l5.293-5.293,1.414,1.414Z\" fill=\"currentColor\"/>\r\n          <path d=\"M9,15H23v2H9Z\" fill=\"currentColor\"/>\r\n          <path d=\"M15,9h2V23H15Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-6 h-6 text-gray-400\" style={{ left: '88%', top: '38%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M11.622,11.238V9.2a.4.4,0,0,1,.4-.4H20.4a.4.4,0,0,1,.4.4v2.036a.4.4,0,0,1-.4.4H12.022A.4.4,0,0,1,11.622,11.238Z\" fill=\"currentColor\"/>\r\n          <path d=\"M11.622,15.6V13.564a.4.4,0,0,1,.4-.4H20.4a.4.4,0,0,1,.4.4V15.6a.4.4,0,0,1-.4.4H12.022A.4.4,0,0,1,11.622,15.6Z\" fill=\"currentColor\"/>\r\n          <path d=\"M11.622,19.964V17.928a.4.4,0,0,1,.4-.4H20.4a.4.4,0,0,1,.4.4v2.036a.4.4,0,0,1-.4.4H12.022A.4.4,0,0,1,11.622,19.964Z\" fill=\"currentColor\"/>\r\n          <path d=\"M7.2,11.238V9.2a.4.4,0,0,1,.4-.4H9.636a.4.4,0,0,1,.4.4v2.036a.4.4,0,0,1-.4.4H7.6A.4.4,0,0,1,7.2,11.238Z\" fill=\"currentColor\"/>\r\n          <path d=\"M7.2,15.6V13.564a.4.4,0,0,1,.4-.4H9.636a.4.4,0,0,1,.4.4V15.6a.4.4,0,0,1-.4.4H7.6A.4.4,0,0,1,7.2,15.6Z\" fill=\"currentColor\"/>\r\n          <path d=\"M7.2,19.964V17.928a.4.4,0,0,1,.4-.4H9.636a.4.4,0,0,1,.4.4v2.036a.4.4,0,0,1-.4.4H7.6A.4.4,0,0,1,7.2,19.964Z\" fill=\"currentColor\"/>\r\n          <path d=\"M22.4,11.238V9.2a.4.4,0,0,1,.4-.4h2.036a.4.4,0,0,1,.4.4v2.036a.4.4,0,0,1-.4.4H22.8A.4.4,0,0,1,22.4,11.238Z\" fill=\"currentColor\"/>\r\n          <path d=\"M22.4,15.6V13.564a.4.4,0,0,1,.4-.4h2.036a.4.4,0,0,1,.4.4V15.6a.4.4,0,0,1-.4.4H22.8A.4.4,0,0,1,22.4,15.6Z\" fill=\"currentColor\"/>\r\n          <path d=\"M22.4,19.964V17.928a.4.4,0,0,1,.4-.4h2.036a.4.4,0,0,1,.4.4v2.036a.4.4,0,0,1-.4.4H22.8A.4.4,0,0,1,22.4,19.964Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-8 h-8 text-gray-400\" style={{ left: '12%', top: '45%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M27.58,4.42a1,1,0,0,0-1.41,0L16,14.59,5.83,4.42A1,1,0,0,0,4.42,5.83L14.59,16,4.42,26.17a1,1,0,1,0,1.41,1.41L16,17.41,26.17,27.58a1,1,0,0,0,1.41-1.41L17.41,16,27.58,5.83A1,1,0,0,0,27.58,4.42Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-6 h-6 text-gray-400\" style={{ left: '82%', top: '48%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm6.21,20.71L16,18.5l-6.21,4.21A1,1,0,0,1,8.5,21.29L14.71,17,8.5,12.71a1,1,0,0,1,1.29-1.42L16,15.5l6.21-4.21a1,1,0,0,1,1.29,1.42L17.29,17l6.21,4.29a1,1,0,0,1-1.29,1.42Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-8 h-8 text-gray-400\" style={{ left: '6%', top: '55%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M2.585,17.413a1.999,1.999,0,0,1,0-2.826L14.587,2.585a1.999,1.999,0,0,1,2.826,0L29.415,14.587a1.999,1.999,0,0,1,0,2.826L17.413,29.415a1.999,1.999,0,0,1-2.826,0Z\" fill=\"currentColor\"/>\r\n          <path d=\"M16,6.5A9.5,9.5,0,1,0,25.5,16,9.51,9.51,0,0,0,16,6.5Zm0,17A7.5,7.5,0,1,1,23.5,16,7.508,7.508,0,0,1,16,23.5Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-6 h-6 text-gray-400\" style={{ left: '86%', top: '58%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M16,2.5C8.544,2.5,2.5,8.544,2.5,16S8.544,29.5,16,29.5,29.5,23.456,29.5,16,23.456,2.5,16,2.5Zm7.7,11.873c-.393.8-.98,1.415-1.793,1.877a5.53,5.53,0,0,1-2.7.693,5.53,5.53,0,0,1-2.7-.693c-.813-.462-1.4-1.077-1.793-1.877a4.5,4.5,0,0,1-.287-1.6,4.5,4.5,0,0,1,.287-1.6c.393-.8.98-1.415,1.793-1.877a5.53,5.53,0,0,1,2.7-.693,5.53,5.53,0,0,1,2.7.693c.813.462,1.4,1.077,1.793,1.877a4.5,4.5,0,0,1,.287,1.6A4.5,4.5,0,0,1,23.7,14.373Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-8 h-8 text-gray-400\" style={{ left: '10%', top: '88%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M24.4,9.7V8.5a.5.5,0,0,0-.5-.5H8.1a.5.5,0,0,0-.5.5V9.7a.5.5,0,0,0,.5.5H23.9A.5.5,0,0,0,24.4,9.7Z\" fill=\"currentColor\"/>\r\n          <path d=\"M24.4,15.7V14.5a.5.5,0,0,0-.5-.5H8.1a.5.5,0,0,0-.5.5v1.2a.5.5,0,0,0,.5.5H23.9A.5.5,0,0,0,24.4,15.7Z\" fill=\"currentColor\"/>\r\n          <path d=\"M24.4,21.7V20.5a.5.5,0,0,0-.5-.5H8.1a.5.5,0,0,0-.5.5v1.2a.5.5,0,0,0,.5.5H23.9A.5.5,0,0,0,24.4,21.7Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-6 h-6 text-gray-400\" style={{ left: '80%', top: '92%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M13.24,16.13,8.11,10.46A.5.5,0,0,1,8.46,9.7h2.85a.5.5,0,0,1,.35.14l3.59,3.71a.25.25,0,0,0,.35,0l3.59-3.71A.5.5,0,0,1,19.54,9.7h2.85a.5.5,0,0,1,.35.76L17.61,16.13a1,1,0,0,1-1.48,0Z\" fill=\"currentColor\"/>\r\n          <path d=\"M18.76,15.87l5.13,5.67a.5.5,0,0,1-.35.76H20.69a.5.5,0,0,1-.35-.14l-3.59-3.71a.25.25,0,0,0-.35,0l-3.59,3.71a.5.5,0,0,1-.35.14H9.61a.5.5,0,0,1-.35-.76l5.13-5.67a1,1,0,0,1,1.48,0Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n\r\n      {/* Coding Symbols */}\r\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '25%', top: '18%' }}>{ }</div>\r\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '65%', top: '22%' }}>[ ]</div>\r\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '35%', top: '32%' }}>( )</div>\r\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '55%', top: '35%' }}>&lt; &gt;</div>\r\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '45%', top: '42%' }}>;</div>\r\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '25%', top: '52%' }}>:</div>\r\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '65%', top: '55%' }}>→</div>\r\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '35%', top: '62%' }}>===</div>\r\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '55%', top: '68%' }}>!=</div>\r\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '15%', top: '75%' }}>&&</div>\r\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '75%', top: '78%' }}>||</div>\r\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '45%', top: '85%' }}>++</div>\r\n      <div className=\"absolute text-gray-500 text-2xl font-mono\" style={{ left: '25%', top: '92%' }}>--</div>\r\n      <div className=\"absolute text-gray-500 text-xl font-mono\" style={{ left: '65%', top: '88%' }}>?:</div>\r\n\r\n      {/* Additional Tech Icons */}\r\n      <div className=\"absolute w-5 h-5 text-gray-400\" style={{ left: '40%', top: '15%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M16,2A14,14,0,1,0,30,16,14,14,0,0,0,16,2Zm0,25A11,11,0,1,1,27,16,11,11,0,0,1,16,27Z\" fill=\"currentColor\"/>\r\n          <path d=\"M16,8a8,8,0,1,0,8,8A8,8,0,0,0,16,8Zm0,13a5,5,0,1,1,5-5A5,5,0,0,1,16,21Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-5 h-5 text-gray-400\" style={{ left: '60%', top: '25%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M5.8,2A1.8,1.8,0,0,0,4,3.8V28.2A1.8,1.8,0,0,0,5.8,30H26.2A1.8,1.8,0,0,0,28,28.2V3.8A1.8,1.8,0,0,0,26.2,2ZM6,4H26V28H6Z\" fill=\"currentColor\"/>\r\n          <path d=\"M8,8H24v2H8Z\" fill=\"currentColor\"/>\r\n          <path d=\"M10,12H22v2H10Z\" fill=\"currentColor\"/>\r\n          <path d=\"M10,16H20v2H10Z\" fill=\"currentColor\"/>\r\n          <path d=\"M8,20H24v2H8Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-5 h-5 text-gray-400\" style={{ left: '30%', top: '65%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M4,6H28a2,2,0,0,1,2,2V24a2,2,0,0,1-2,2H4a2,2,0,0,1-2-2V8A2,2,0,0,1,4,6ZM4,8V24H28V8Z\" fill=\"currentColor\"/>\r\n          <path d=\"M6,10H26v2H6Z\" fill=\"currentColor\"/>\r\n          <path d=\"M6,14H26v2H6Z\" fill=\"currentColor\"/>\r\n          <path d=\"M6,18H26v2H6Z\" fill=\"currentColor\"/>\r\n          <path d=\"M6,22H26v2H6Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-5 h-5 text-gray-400\" style={{ left: '70%', top: '72%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M5.902,27.201,3.656,2h24.688l-2.249,25.197L15.985,30ZM24.126,5H7.874l1.755,19.683L15.985,26.8l6.356-2.117Z\" fill=\"currentColor\"/>\r\n          <path d=\"M16,8H24l-.4,4H16v4h7.6l-.533,6L16,23.2V19.2l3.067-.8L19.2,17H16v4l-3.067-.8L12.8,17H16V13H8.4L8,9h8Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-5 h-5 text-gray-400\" style={{ left: '20%', top: '82%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M5.902,27.201,3.656,2h24.688l-2.249,25.197L15.985,30ZM24.126,5H7.874l1.755,19.683L15.985,26.8l6.356-2.117Z\" fill=\"currentColor\"/>\r\n          <path d=\"M16,13H8.4L8,9h8V5H24l-.4,4H16v4h7.6l-.533,6L16,23.2V19.2l3.067-.8L19.2,17H16Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n      <div className=\"absolute w-5 h-5 text-gray-400\" style={{ left: '50%', top: '95%' }}>\r\n        <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\" className=\"w-full h-full\">\r\n          <path d=\"M29.472,14.753,17.247,2.528a1.8,1.8,0,0,0-2.55,0L12.158,5.067l3.22,3.22a2.141,2.141,0,0,1,2.712,2.73l3.1,3.1a2.143,2.143,0,1,1-1.285,1.21l-2.895-2.895v7.617a2.141,2.141,0,1,1-1.764-.062V12.3a2.146,2.146,0,0,1-1.165-2.814L10.911,6.314,2.528,14.7a1.8,1.8,0,0,0,0,2.551L14.753,29.472a1.8,1.8,0,0,0,2.55,0L29.472,17.3a1.8,1.8,0,0,0,0-2.551Z\" fill=\"currentColor\"/>\r\n        </svg>\r\n      </div>\r\n    </div>\r\n\r\n    {/* Grid background extending behind header */}\r\n    <div\r\n      className=\"absolute inset-0 -top-20 opacity-20\"\r\n      style={{\r\n        backgroundImage: `\r\n          linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\r\n          linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\r\n        `,\r\n        backgroundSize: '40px 40px'\r\n      }}\r\n    />\r\n    {/* Evenly spaced dashed grid lines that frame content without conflicting */}\r\n    <div className=\"absolute inset-0 -top-20 pointer-events-none z-10\">\r\n      {/* Left vertical dashed line */}\r\n      <div\r\n        className=\"absolute top-0 bottom-0 w-px\"\r\n        style={{\r\n          left: '10%',\r\n          background: 'repeating-linear-gradient(to bottom, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\r\n        }}\r\n      />\r\n\r\n      {/* Right vertical dashed line */}\r\n      <div\r\n        className=\"absolute top-0 bottom-0 w-px\"\r\n        style={{\r\n          right: '10%',\r\n          background: 'repeating-linear-gradient(to bottom, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\r\n        }}\r\n      />\r\n\r\n      {/* Top horizontal dashed line - above content */}\r\n      <div\r\n        className=\"absolute h-px\"\r\n        style={{\r\n          top: '20px',\r\n          left: '10%',\r\n          right: '10%',\r\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\r\n        }}\r\n      />\r\n\r\n      {/* Middle horizontal line - between announcement and headline */}\r\n      <div\r\n        className=\"absolute h-px\"\r\n        style={{\r\n          top: '20%',\r\n          left: '10%',\r\n          right: '10%',\r\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\r\n        }}\r\n      />\r\n\r\n      {/* Bottom horizontal line - below buttons */}\r\n      <div\r\n        className=\"absolute h-px\"\r\n        style={{\r\n          bottom: '20px',\r\n          left: '10%',\r\n          right: '10%',\r\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\r\n        }}\r\n      />\r\n    </div>\r\n\r\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\r\n      <div className=\"flex flex-col items-center justify-center gap-8 pt-6 pb-8 lg:pt-12 lg:pb-12\">\r\n        <div>\r\n          <Feed queries={[blog.latestPostQuery]}>\r\n            {/* biome-ignore lint/suspicious/useAwait: \"Server Actions must be async\" */}\r\n            {async ([data]: [any]) => {\r\n              'use server';\r\n\r\n              return (\r\n                <Button variant=\"secondary\" size=\"sm\" className=\"gap-4 bg-gradient-to-r from-orange-500/10 to-orange-600/10 border-orange-500/20 hover:from-orange-500/20 hover:to-orange-600/20 hover:border-orange-500/30 text-orange-400 hover:text-orange-300\" asChild>\r\n                  <Link href={`/blog/${data.blog.posts.item?._slug}`}>\r\n                    {dictionary.web.home.hero.announcement}{' '}\r\n                    <MoveRight className=\"h-4 w-4 text-orange-400\" />\r\n                  </Link>\r\n                </Button>\r\n              );\r\n            }}\r\n          </Feed>\r\n        </div>\r\n        <div className=\"flex flex-col gap-6 relative\">\r\n          {/* Natural flowing white gradient background extending behind header */}\r\n          <div className=\"absolute inset-0 -top-32 bg-gradient-to-b from-white/8 via-white/8 to-transparent blur-3xl -z-10 scale-150\" />\r\n          <div className=\"absolute inset-0 -top-24 bg-gradient-radial from-white/8 via-white/8 to-transparent blur-2xl -z-10 scale-125\" />\r\n          <AnimatedTitle />\r\n          <p className=\"max-w-3xl mx-auto text-center text-lg text-muted-foreground leading-relaxed tracking-tight md:text-xl relative z-10\">\r\n            Meet Cubent Coder, the autonomous AI coding assistant that lives in your editor. Generate code, debug issues, write documentation, and automate tasks with natural language commands.\r\n          </p>\r\n        </div>\r\n        <div className=\"flex flex-row gap-4 mt-2\">\r\n          <Button size=\"lg\" className=\"gap-4 bg-black border border-gray-800 text-white hover:bg-gray-900 rounded-full px-8 py-4 text-lg font-medium\" asChild>\r\n            <Link href=\"https://marketplace.visualstudio.com/items?itemName=cubent.cubent\" target=\"_blank\" rel=\"noopener noreferrer\">\r\n              <svg className=\"w-6 h-6\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                <path d=\"M23.15 2.587L18.21.21a1.494 1.494 0 0 0-1.705.29l-9.46 8.63-4.12-3.128a.999.999 0 0 0-1.276.057L.327 7.261A1 1 0 0 0 .326 8.74L3.899 12 .326 15.26a1 1 0 0 0 .001 1.479L1.65 17.94a.999.999 0 0 0 1.276.057l4.12-3.128 9.46 8.63a1.492 1.492 0 0 0 1.704.29l4.942-2.377A1.5 1.5 0 0 0 24 20.06V3.939a1.5 1.5 0 0 0-.85-1.352zm-5.146 14.861L10.826 12l7.178-5.448v10.896z\"/>\r\n              </svg>\r\n              VS Code\r\n            </Link>\r\n          </Button>\r\n          <Button size=\"lg\" className=\"gap-4 bg-black border border-gray-800 text-white hover:bg-gray-900 rounded-full px-8 py-4 text-lg font-medium\" asChild>\r\n            <Link href=\"https://plugins.jetbrains.com/plugin/cubent\" target=\"_blank\" rel=\"noopener noreferrer\">\r\n              <svg className=\"w-6 h-6\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                <path d=\"M0 0v24h24V0H0zm3.723 3.111h5v1.834h-1.39v6.277h1.39v1.834h-5v-1.834h1.444V4.945H3.723V3.111zm11.055 0H17v1.834h-1.389v6.277H17v1.834h-2.222V3.111zm-8.334 8.944H9.61v1.833H6.444v-1.833z\"/>\r\n              </svg>\r\n              JetBrains\r\n            </Link>\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;;;;;;;;;;MA8Na,wEAAO,CAAC,KAAY;;IAGnB,qBACE,6VAAC,2JAAA,CAAA,SAAM;QAAC,SAAQ;QAAY,MAAK;QAAK,WAAU;QAAmM,OAAO;kBACxP,cAAA,6VAAC,2QAAA,CAAA,UAAI;YAAC,MAAM,CAAC,MAAM,EAAE,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO;;;gBACR;8BACxC,6VAAC,oSAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B;AAlOL,MAAM,OAAO,OAAO,EAAE,UAAU,EAAa,iBAClD,6VAAC;QAAI,WAAU;;0BAEb,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAM,KAAK;wBAAM;kCAC9E,cAAA,6VAAC;4BAAI,OAAM;4BAAK,QAAO;4BAAK,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCAC3F,cAAA,6VAAC;gCAAK,GAAE;gCAAktB,MAAK;gCAAe,aAAY;;;;;;;;;;;;;;;;kCAG9vB,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAqT,MAAK;;;;;;8CAClU,6VAAC;oCAAK,GAAE;oCAAmX,MAAK;;;;;;;;;;;;;;;;;kCAGpY,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAAK,QAAO;4BAAK,SAAQ;4BAAY,OAAM;4BAA6B,WAAU;sCAC3F,cAAA,6VAAC;gCAAK,GAAE;gCAAolB,MAAK;gCAAe,aAAY;;;;;;;;;;;;;;;;kCAGhoB,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAA0J,MAAK;;;;;;8CACvK,6VAAC;oCAAK,GAAE;oCAAsC,MAAK;;;;;;8CACnD,6VAAC;oCAAK,GAAE;oCAAghB,MAAK;;;;;;8CAC7hB,6VAAC;oCAAK,GAAE;oCAAif,MAAK;;;;;;;;;;;;;;;;;kCAGlgB,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAM,KAAK;wBAAM;kCAC9E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAuF,MAAK;;;;;;8CACpG,6VAAC;oCAAK,GAAE;oCAAyE,MAAK;;;;;;8CACtF,6VAAC;oCAAK,GAAE;oCAAgB,MAAK;;;;;;8CAC7B,6VAAC;oCAAK,GAAE;oCAAiB,MAAK;;;;;;;;;;;;;;;;;kCAGlC,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAkH,MAAK;;;;;;8CAC/H,6VAAC;oCAAK,GAAE;oCAAgH,MAAK;;;;;;8CAC7H,6VAAC;oCAAK,GAAE;oCAAqH,MAAK;;;;;;8CAClI,6VAAC;oCAAK,GAAE;oCAA0G,MAAK;;;;;;8CACvH,6VAAC;oCAAK,GAAE;oCAAwG,MAAK;;;;;;8CACrH,6VAAC;oCAAK,GAAE;oCAA6G,MAAK;;;;;;8CAC1H,6VAAC;oCAAK,GAAE;oCAA6G,MAAK;;;;;;8CAC1H,6VAAC;oCAAK,GAAE;oCAA2G,MAAK;;;;;;8CACxH,6VAAC;oCAAK,GAAE;oCAAgH,MAAK;;;;;;;;;;;;;;;;;kCAGjI,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAiM,MAAK;;;;;;;;;;;;;;;;kCAGlN,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAmN,MAAK;;;;;;;;;;;;;;;;kCAGpO,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAM,KAAK;wBAAM;kCAC9E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAkK,MAAK;;;;;;8CAC/K,6VAAC;oCAAK,GAAE;oCAA8G,MAAK;;;;;;;;;;;;;;;;;kCAG/H,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAua,MAAK;;;;;;;;;;;;;;;;kCAGxb,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAmG,MAAK;;;;;;8CAChH,6VAAC;oCAAK,GAAE;oCAAsG,MAAK;;;;;;8CACnH,6VAAC;oCAAK,GAAE;oCAAsG,MAAK;;;;;;;;;;;;;;;;;kCAGvH,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAsL,MAAK;;;;;;8CACnM,6VAAC;oCAAK,GAAE;oCAAmL,MAAK;;;;;;;;;;;;;;;;;kCAKpM,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;;;;;;kCAC5F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC9F,6VAAC;wBAAI,WAAU;wBAA4C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAC/F,6VAAC;wBAAI,WAAU;wBAA2C,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAAG;;;;;;kCAG9F,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAsF,MAAK;;;;;;8CACnG,6VAAC;oCAAK,GAAE;oCAA0E,MAAK;;;;;;;;;;;;;;;;;kCAG3F,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAyH,MAAK;;;;;;8CACtI,6VAAC;oCAAK,GAAE;oCAAe,MAAK;;;;;;8CAC5B,6VAAC;oCAAK,GAAE;oCAAkB,MAAK;;;;;;8CAC/B,6VAAC;oCAAK,GAAE;oCAAkB,MAAK;;;;;;8CAC/B,6VAAC;oCAAK,GAAE;oCAAgB,MAAK;;;;;;;;;;;;;;;;;kCAGjC,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAAuF,MAAK;;;;;;8CACpG,6VAAC;oCAAK,GAAE;oCAAgB,MAAK;;;;;;8CAC7B,6VAAC;oCAAK,GAAE;oCAAgB,MAAK;;;;;;8CAC7B,6VAAC;oCAAK,GAAE;oCAAgB,MAAK;;;;;;8CAC7B,6VAAC;oCAAK,GAAE;oCAAgB,MAAK;;;;;;;;;;;;;;;;;kCAGjC,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAA6G,MAAK;;;;;;8CAC1H,6VAAC;oCAAK,GAAE;oCAAwG,MAAK;;;;;;;;;;;;;;;;;kCAGzH,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;;8CACpE,6VAAC;oCAAK,GAAE;oCAA6G,MAAK;;;;;;8CAC1H,6VAAC;oCAAK,GAAE;oCAAiF,MAAK;;;;;;;;;;;;;;;;;kCAGlG,6VAAC;wBAAI,WAAU;wBAAiC,OAAO;4BAAE,MAAM;4BAAO,KAAK;wBAAM;kCAC/E,cAAA,6VAAC;4BAAI,OAAM;4BAA6B,SAAQ;4BAAY,WAAU;sCACpE,cAAA,6VAAC;gCAAK,GAAE;gCAAmV,MAAK;;;;;;;;;;;;;;;;;;;;;;0BAMtW,6VAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC;;;QAGlB,CAAC;oBACD,gBAAgB;gBAClB;;;;;;0BAGF,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,MAAM;4BACN,YAAY;wBACd;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,YAAY;wBACd;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;kCAIF,6VAAC;wBACC,WAAU;wBACV,OAAO;4BACL,QAAQ;4BACR,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;;;;;;;;;;;;0BAIJ,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;sCACC,cAAA,6VAAC,sLAAA,CAAA,OAAI;gCAAC,SAAS;oCAAC,wHAAA,CAAA,OAAI,CAAC,eAAe;iCAAC;0CAElC,8VAAA;;;;;;;;;;;sCAcL,6VAAC;4BAAI,WAAU;;8CAEb,6VAAC;oCAAI,WAAU;;;;;;8CACf,6VAAC;oCAAI,WAAU;;;;;;8CACf,6VAAC,gLAAA,CAAA,gBAAa;;;;;8CACd,6VAAC;oCAAE,WAAU;8CAAsH;;;;;;;;;;;;sCAIrI,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,2JAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;oCAAgH,OAAO;8CACjJ,cAAA,6VAAC,2QAAA,CAAA,UAAI;wCAAC,MAAK;wCAAoE,QAAO;wCAAS,KAAI;;0DACjG,6VAAC;gDAAI,WAAU;gDAAU,SAAQ;gDAAY,MAAK;0DAChD,cAAA,6VAAC;oDAAK,GAAE;;;;;;;;;;;4CACJ;;;;;;;;;;;;8CAIV,6VAAC,2JAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,WAAU;oCAAgH,OAAO;8CACjJ,cAAA,6VAAC,2QAAA,CAAA,UAAI;wCAAC,MAAK;wCAA8C,QAAO;wCAAS,KAAI;;0DAC3E,6VAAC;gDAAI,WAAU;gDAAU,SAAQ;gDAAY,MAAK;0DAChD,cAAA,6VAAC;oDAAK,GAAE;;;;;;;;;;;4CACJ", "debugId": null}}, {"offset": {"line": 1434, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/analytics/posthog/server.ts"], "sourcesContent": ["import 'server-only';\nimport { PostHog } from 'posthog-node';\nimport { keys } from '../keys';\n\nconst envKeys = keys();\n\nexport const analytics = envKeys.NEXT_PUBLIC_POSTHOG_KEY && envKeys.NEXT_PUBLIC_POSTHOG_HOST\n  ? new PostHog(envKeys.NEXT_PUBLIC_POSTHOG_KEY, {\n      host: envKeys.NEXT_PUBLIC_POSTHOG_HOST,\n\n      // Don't batch events and flush immediately - we're running in a serverless environment\n      flushAt: 1,\n      flushInterval: 0,\n    })\n  : null;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,CAAA,GAAA,6HAAA,CAAA,OAAI,AAAD;AAEZ,MAAM,YAAY,QAAQ,uBAAuB,IAAI,QAAQ,wBAAwB,GACxF,IAAI,qNAAA,CAAA,UAAO,CAAC,QAAQ,uBAAuB,EAAE;IAC3C,MAAM,QAAQ,wBAAwB;IAEtC,uFAAuF;IACvF,SAAS;IACT,eAAe;AACjB,KACA", "debugId": null}}, {"offset": {"line": 1456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/auth/server.ts"], "sourcesContent": ["import 'server-only';\n\nexport * from '@clerk/nextjs/server';\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/feature-flags/lib/create-flag.ts"], "sourcesContent": ["import { analytics } from '@repo/analytics/posthog/server';\nimport { auth } from '@repo/auth/server';\nimport { flag } from 'flags/next';\n\nexport const createFlag = (key: string) =>\n  flag({\n    key,\n    defaultValue: false,\n    async decide() {\n      const { userId } = await auth();\n\n      if (!userId) {\n        return this.defaultValue as boolean;\n      }\n\n      const isEnabled = analytics\n        ? await analytics.isFeatureEnabled(key, userId)\n        : null;\n\n      return isEnabled ?? (this.defaultValue as boolean);\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,MAAM,aAAa,CAAC,MACzB,CAAA,GAAA,8OAAA,CAAA,OAAI,AAAD,EAAE;QACH;QACA,cAAc;QACd,MAAM;YACJ,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,CAAA,GAAA,6RAAA,CAAA,OAAI,AAAD;YAE5B,IAAI,CAAC,QAAQ;gBACX,OAAO,IAAI,CAAC,YAAY;YAC1B;YAEA,MAAM,YAAY,0IAAA,CAAA,YAAS,GACvB,MAAM,0IAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC,KAAK,UACtC;YAEJ,OAAO,aAAc,IAAI,CAAC,YAAY;QACxC;IACF", "debugId": null}}, {"offset": {"line": 1521, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/feature-flags/index.ts"], "sourcesContent": ["import { createFlag } from './lib/create-flag';\n\nexport const showBetaFeature = createFlag('showBetaFeature');\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 1533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/seo/metadata.ts"], "sourcesContent": ["import merge from 'lodash.merge';\nimport type { Metadata } from 'next';\n\ntype MetadataGenerator = Omit<Metadata, 'description' | 'title'> & {\n  title: string;\n  description: string;\n  image?: string;\n};\n\nconst applicationName = 'next-forge';\nconst author: Metadata['authors'] = {\n  name: 'Vercel',\n  url: 'https://vercel.com/',\n};\nconst publisher = 'Vercel';\nconst twitterHandle = '@vercel';\nconst protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';\nconst productionUrl = process.env.VERCEL_PROJECT_PRODUCTION_URL;\n\nexport const createMetadata = ({\n  title,\n  description,\n  image,\n  ...properties\n}: MetadataGenerator): Metadata => {\n  const parsedTitle = `${title} | ${applicationName}`;\n  const defaultMetadata: Metadata = {\n    title: parsedTitle,\n    description,\n    applicationName,\n    metadataBase: productionUrl\n      ? new URL(`${protocol}://${productionUrl}`)\n      : undefined,\n    authors: [author],\n    creator: author.name,\n    formatDetection: {\n      telephone: false,\n    },\n    appleWebApp: {\n      capable: true,\n      statusBarStyle: 'default',\n      title: parsedTitle,\n    },\n    openGraph: {\n      title: parsedTitle,\n      description,\n      type: 'website',\n      siteName: applicationName,\n      locale: 'en_US',\n    },\n    publisher,\n    twitter: {\n      card: 'summary_large_image',\n      creator: twitterHandle,\n    },\n  };\n\n  const metadata: Metadata = merge(defaultMetadata, properties);\n\n  if (image && metadata.openGraph) {\n    metadata.openGraph.images = [\n      {\n        url: image,\n        width: 1200,\n        height: 630,\n        alt: title,\n      },\n    ];\n  }\n\n  return metadata;\n};\n"], "names": [], "mappings": ";;;AAAA;;AASA,MAAM,kBAAkB;AACxB,MAAM,SAA8B;IAClC,MAAM;IACN,KAAK;AACP;AACA,MAAM,YAAY;AAClB,MAAM,gBAAgB;AACtB,MAAM,WAAW,6EAAkD;AACnE,MAAM,gBAAgB,QAAQ,GAAG,CAAC,6BAA6B;AAExD,MAAM,iBAAiB,CAAC,EAC7B,KAAK,EACL,WAAW,EACX,KAAK,EACL,GAAG,YACe;IAClB,MAAM,cAAc,GAAG,MAAM,GAAG,EAAE,iBAAiB;IACnD,MAAM,kBAA4B;QAChC,OAAO;QACP;QACA;QACA,cAAc,gBACV,IAAI,IAAI,GAAG,SAAS,GAAG,EAAE,eAAe,IACxC;QACJ,SAAS;YAAC;SAAO;QACjB,SAAS,OAAO,IAAI;QACpB,iBAAiB;YACf,WAAW;QACb;QACA,aAAa;YACX,SAAS;YACT,gBAAgB;YAChB,OAAO;QACT;QACA,WAAW;YACT,OAAO;YACP;YACA,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;IACF;IAEA,MAAM,WAAqB,CAAA,GAAA,oMAAA,CAAA,UAAK,AAAD,EAAE,iBAAiB;IAElD,IAAI,SAAS,SAAS,SAAS,EAAE;QAC/B,SAAS,SAAS,CAAC,MAAM,GAAG;YAC1B;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;IACH;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1607, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Community = registerClientReference(\n    function() { throw new Error(\"Attempted to call Community() from the server but Community is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/community.tsx <module evaluation>\",\n    \"Community\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uFACA", "debugId": null}}, {"offset": {"line": 1621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Community = registerClientReference(\n    function() { throw new Error(\"Attempted to call Community() from the server but Community is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/community.tsx\",\n    \"Community\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mEACA", "debugId": null}}, {"offset": {"line": 1635, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/cta.tsx"], "sourcesContent": ["import { env } from '@/env';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport type { Dictionary } from '@repo/internationalization';\nimport { MoveRight, PhoneCall } from 'lucide-react';\nimport Link from 'next/link';\n\ntype CTAProps = {\n  dictionary: Dictionary;\n};\n\nexport const CTA = ({ dictionary }: CTAProps) => (\n  <div className=\"w-full py-20 lg:py-40\">\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n      <div className=\"flex flex-col items-center gap-8 rounded-md bg-muted p-4 text-center lg:p-14\">\n        <div className=\"flex flex-col gap-2\">\n          <h3 className=\"max-w-xl font-regular text-3xl tracking-tighter md:text-5xl\">\n            {dictionary.web.home.cta.title}\n          </h3>\n          <p className=\"max-w-xl text-lg text-muted-foreground leading-relaxed tracking-tight\">\n            {dictionary.web.home.cta.description}\n          </p>\n        </div>\n        <div className=\"flex flex-row gap-4\">\n          <Button className=\"gap-4\" variant=\"outline\" asChild>\n            <Link href=\"/contact\">\n              {dictionary.web.global.primaryCta}{' '}\n              <PhoneCall className=\"h-4 w-4\" />\n            </Link>\n          </Button>\n          <Button className=\"gap-4\" asChild>\n            <Link href={env.NEXT_PUBLIC_APP_URL}>\n              {dictionary.web.global.secondaryCta}{' '}\n              <MoveRight className=\"h-4 w-4\" />\n            </Link>\n          </Button>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AAAA;AACA;;;;;;AAMO,MAAM,MAAM,CAAC,EAAE,UAAU,EAAY,iBAC1C,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAG,WAAU;0CACX,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;;;;;;0CAEhC,6VAAC;gCAAE,WAAU;0CACV,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW;;;;;;;;;;;;kCAGxC,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,2JAAA,CAAA,SAAM;gCAAC,WAAU;gCAAQ,SAAQ;gCAAU,OAAO;0CACjD,cAAA,6VAAC,2QAAA,CAAA,UAAI;oCAAC,MAAK;;wCACR,WAAW,GAAG,CAAC,MAAM,CAAC,UAAU;wCAAE;sDACnC,6VAAC,oSAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGzB,6VAAC,2JAAA,CAAA,SAAM;gCAAC,WAAU;gCAAQ,OAAO;0CAC/B,cAAA,6VAAC,2QAAA,CAAA,UAAI;oCAAC,MAAM,kHAAA,CAAA,MAAG,CAAC,mBAAmB;;wCAChC,WAAW,GAAG,CAAC,MAAM,CAAC,YAAY;wCAAE;sDACrC,6VAAC,oSAAA,CAAA,YAAS;4CAAC,WAAU", "debugId": null}}, {"offset": {"line": 1784, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/accordion.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/accordion.tsx <module evaluation>\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oFACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,oFACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,oFACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,oFACA", "debugId": null}}, {"offset": {"line": 1810, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/accordion.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/accordion.tsx\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/accordion.tsx\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/accordion.tsx\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/packages/design-system/components/ui/accordion.tsx\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,gEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,gEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,gEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,gEACA", "debugId": null}}, {"offset": {"line": 1836, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1846, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/faq.tsx"], "sourcesContent": ["import {\n  Accordion,\n  AccordionContent,\n  Accordion<PERSON><PERSON>,\n  AccordionTrigger,\n} from '@repo/design-system/components/ui/accordion';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport type { Dictionary } from '@repo/internationalization';\nimport { PhoneCall } from 'lucide-react';\nimport Link from 'next/link';\n\ntype FAQProps = {\n  dictionary: Dictionary;\n};\n\nexport const FAQ = ({ dictionary }: FAQProps) => (\n  <div className=\"w-full py-20 lg:py-40\">\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n      <div className=\"grid gap-10 lg:grid-cols-2\">\n        <div className=\"flex flex-col gap-10\">\n          <div className=\"flex flex-col gap-4\">\n            <div className=\"flex flex-col gap-2\">\n              <h4 className=\"max-w-xl text-left font-regular text-3xl tracking-tighter md:text-5xl\">\n                {dictionary.web.home.faq.title}\n              </h4>\n              <p className=\"max-w-xl text-left text-lg text-muted-foreground leading-relaxed tracking-tight lg:max-w-lg\">\n                {dictionary.web.home.faq.description}\n              </p>\n            </div>\n            <div className=\"\">\n              <Button className=\"gap-4\" variant=\"outline\" asChild>\n                <Link href=\"/contact\">\n                  {dictionary.web.home.faq.cta}{' '}\n                  <PhoneCall className=\"h-4 w-4\" />\n                </Link>\n              </Button>\n            </div>\n          </div>\n        </div>\n        <Accordion type=\"single\" collapsible className=\"w-full\">\n          {dictionary.web.home.faq.items.map((item, index) => (\n            <AccordionItem key={index} value={`index-${index}`}>\n              <AccordionTrigger>{item.question}</AccordionTrigger>\n              <AccordionContent>{item.answer}</AccordionContent>\n            </AccordionItem>\n          ))}\n        </Accordion>\n      </div>\n    </div>\n  </div>\n);\n"], "names": [], "mappings": ";;;;AAAA;AAMA;AAEA;AACA;;;;;;AAMO,MAAM,MAAM,CAAC,EAAE,UAAU,EAAY,iBAC1C,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAG,WAAU;sDACX,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK;;;;;;sDAEhC,6VAAC;4CAAE,WAAU;sDACV,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,WAAW;;;;;;;;;;;;8CAGxC,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC,2JAAA,CAAA,SAAM;wCAAC,WAAU;wCAAQ,SAAQ;wCAAU,OAAO;kDACjD,cAAA,6VAAC,2QAAA,CAAA,UAAI;4CAAC,MAAK;;gDACR,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;gDAAE;8DAC9B,6VAAC,oSAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM/B,6VAAC,8JAAA,CAAA,YAAS;wBAAC,MAAK;wBAAS,WAAW;wBAAC,WAAU;kCAC5C,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxC,6VAAC,8JAAA,CAAA,gBAAa;gCAAa,OAAO,CAAC,MAAM,EAAE,OAAO;;kDAChD,6VAAC,8JAAA,CAAA,mBAAgB;kDAAE,KAAK,QAAQ;;;;;;kDAChC,6VAAC,8JAAA,CAAA,mBAAgB;kDAAE,KAAK,MAAM;;;;;;;+BAFZ", "debugId": null}}, {"offset": {"line": 1995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Mockup = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mockup() from the server but <PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx <module evaluation>\",\n    \"Mockup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,oFACA", "debugId": null}}, {"offset": {"line": 2009, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Mockup = registerClientReference(\n    function() { throw new Error(\"Attempted to call Mockup() from the server but <PERSON><PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/mockup.tsx\",\n    \"Mockup\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,gEACA", "debugId": null}}, {"offset": {"line": 2023, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/stats.tsx"], "sourcesContent": ["import type { Dictionary } from '@repo/internationalization';\nimport { MoveDownLeft, MoveUpRight } from 'lucide-react';\n\ntype StatsProps = {\n  dictionary: Dictionary;\n};\n\nexport const Stats = ({ dictionary }: StatsProps) => (\n  <div className=\"w-full py-20 lg:py-40\">\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n      <div className=\"grid grid-cols-1 gap-10 lg:grid-cols-2\">\n        <div className=\"flex flex-col items-start gap-4\">\n          <div className=\"flex flex-col gap-2\">\n            <h2 className=\"text-left font-regular text-xl tracking-tighter md:text-5xl lg:max-w-xl\">\n              {dictionary.web.home.stats.title}\n            </h2>\n            <p className=\"text-left text-lg text-muted-foreground leading-relaxed tracking-tight lg:max-w-sm\">\n              {dictionary.web.home.stats.description}\n            </p>\n          </div>\n        </div>\n        <div className=\"flex items-center justify-center\">\n          <div className=\"grid w-full grid-cols-1 gap-2 text-left sm:grid-cols-2 lg:grid-cols-2\">\n            {dictionary.web.home.stats.items.map((item, index) => (\n              <div\n                className=\"flex flex-col justify-between gap-0 rounded-md border p-6\"\n                key={index}\n              >\n                {Number.parseFloat(item.delta) > 0 ? (\n                  <MoveUpRight className=\"mb-10 h-4 w-4 text-primary\" />\n                ) : (\n                  <MoveDownLeft className=\"mb-10 h-4 w-4 text-destructive\" />\n                )}\n                <h2 className=\"flex max-w-xl flex-row items-end gap-4 text-left font-regular text-4xl tracking-tighter\">\n                  {item.type === 'currency' && '$'}\n                  {new Intl.NumberFormat().format(\n                    Number.parseFloat(item.metric)\n                  )}\n                  <span className=\"text-muted-foreground text-sm tracking-normal\">\n                    {Number.parseFloat(item.delta) > 0 ? '+' : ''}\n                    {item.delta}%\n                  </span>\n                </h2>\n                <p className=\"max-w-xl text-left text-base text-muted-foreground leading-relaxed tracking-tight\">\n                  {item.title}\n                </p>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n"], "names": [], "mappings": ";;;;AACA;AAAA;;;AAMO,MAAM,QAAQ,CAAC,EAAE,UAAU,EAAc,iBAC9C,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;;;;;;8CAElC,6VAAC;oCAAE,WAAU;8CACV,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW;;;;;;;;;;;;;;;;;kCAI5C,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC1C,6VAAC;oCACC,WAAU;;wCAGT,OAAO,UAAU,CAAC,KAAK,KAAK,IAAI,kBAC/B,6VAAC,4SAAA,CAAA,cAAW;4CAAC,WAAU;;;;;iEAEvB,6VAAC,8SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;sDAE1B,6VAAC;4CAAG,WAAU;;gDACX,KAAK,IAAI,KAAK,cAAc;gDAC5B,IAAI,KAAK,YAAY,GAAG,MAAM,CAC7B,OAAO,UAAU,CAAC,KAAK,MAAM;8DAE/B,6VAAC;oDAAK,WAAU;;wDACb,OAAO,UAAU,CAAC,KAAK,KAAK,IAAI,IAAI,MAAM;wDAC1C,KAAK,KAAK;wDAAC;;;;;;;;;;;;;sDAGhB,6VAAC;4CAAE,WAAU;sDACV,KAAK,KAAK;;;;;;;mCAlBR", "debugId": null}}, {"offset": {"line": 2169, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/testimonials.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Testimonials = registerClientReference(\n    function() { throw new Error(\"Attempted to call Testimonials() from the server but Testimonials is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/testimonials.tsx <module evaluation>\",\n    \"Testimonials\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0FACA", "debugId": null}}, {"offset": {"line": 2183, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/testimonials.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Testimonials = registerClientReference(\n    function() { throw new Error(\"Attempted to call Testimonials() from the server but Testimonials is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/testimonials.tsx\",\n    \"Testimonials\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,eAAe,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,sEACA", "debugId": null}}, {"offset": {"line": 2197, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrustedBy = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrustedBy() from the server but TrustedBy is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/trusted-by.tsx <module evaluation>\",\n    \"TrustedBy\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wFACA", "debugId": null}}, {"offset": {"line": 2221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const TrustedBy = registerClientReference(\n    function() { throw new Error(\"Attempted to call TrustedBy() from the server but TrustedBy is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/apps/web/app/[locale]/(home)/components/trusted-by.tsx\",\n    \"TrustedBy\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,oWAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oEACA", "debugId": null}}, {"offset": {"line": 2235, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/page.tsx"], "sourcesContent": ["import { showBetaFeature } from '@repo/feature-flags';\nimport { getDictionary } from '@repo/internationalization';\nimport { createMetadata } from '@repo/seo/metadata';\nimport type { Metadata } from 'next';\nimport AdvancedFeatures from './components/advanced-features';\nimport { Community } from './components/community';\nimport { CTA } from './components/cta';\nimport ExtensionTools from './components/extension-tools';\nimport { FAQ } from './components/faq';\n\nimport { Hero } from './components/hero';\nimport { Mockup } from './components/mockup';\nimport { Stats } from './components/stats';\nimport { Testimonials } from './components/testimonials';\nimport { TrustedBy } from './components/trusted-by';\n\ntype HomeProps = {\n  params: Promise<{\n    locale: string;\n  }>;\n};\n\nexport const generateMetadata = async ({\n  params,\n}: HomeProps): Promise<Metadata> => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n\n  return createMetadata(dictionary.web.home.meta);\n};\n\nconst Home = async ({ params }: HomeProps) => {\n  const { locale } = await params;\n  const dictionary = await getDictionary(locale);\n  const betaFeature = await showBetaFeature();\n\n  return (\n    <>\n      {betaFeature && (\n        <div className=\"w-full bg-black py-2 text-center text-white\">\n          Beta feature now available\n        </div>\n      )}\n      <Hero dictionary={dictionary} />\n      <TrustedBy dictionary={dictionary} />\n      <Mockup />\n      <ExtensionTools />\n      <AdvancedFeatures />\n      <Community dictionary={dictionary} />\n      <Stats dictionary={dictionary} />\n      <Testimonials dictionary={dictionary} />\n      <FAQ dictionary={dictionary} />\n      <CTA dictionary={dictionary} />\n    </>\n  );\n};\n\nexport default Home;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAQO,MAAM,mBAAmB,OAAO,EACrC,MAAM,EACI;IACV,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IAEvC,OAAO,CAAA,GAAA,2HAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,IAAI;AAChD;AAEA,MAAM,OAAO,OAAO,EAAE,MAAM,EAAa;IACvC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,aAAa,MAAM,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IACvC,MAAM,cAAc,MAAM,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IAExC,qBACE;;YACG,6BACC,6VAAC;gBAAI,WAAU;0BAA8C;;;;;;0BAI/D,6VAAC,mKAAA,CAAA,OAAI;gBAAC,YAAY;;;;;;0BAClB,6VAAC,4KAAA,CAAA,YAAS;gBAAC,YAAY;;;;;;0BACvB,6VAAC,qKAAA,CAAA,SAAM;;;;;0BACP,6VAAC,iLAAA,CAAA,UAAc;;;;;0BACf,6VAAC,mLAAA,CAAA,UAAgB;;;;;0BACjB,6VAAC,wKAAA,CAAA,YAAS;gBAAC,YAAY;;;;;;0BACvB,6VAAC,oKAAA,CAAA,QAAK;gBAAC,YAAY;;;;;;0BACnB,6VAAC,2KAAA,CAAA,eAAY;gBAAC,YAAY;;;;;;0BAC1B,6VAAC,kKAAA,CAAA,MAAG;gBAAC,YAAY;;;;;;0BACjB,6VAAC,kKAAA,CAAA,MAAG;gBAAC,YAAY;;;;;;;;AAGvB;uCAEe", "debugId": null}}]}