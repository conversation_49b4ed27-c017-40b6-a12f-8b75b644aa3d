{"generatedAt": "2025-06-30T19:27:03.338Z", "sdkVersion": "8.2.7", "inputHash": "f62946efa3c8670367e359b399fa0c58", "schemaHash": "e23776eacd74956f6122e4e52fdb0a26", "resolvedRef": {"repoHash": "57ec52db", "type": "branch", "ref": "main", "createSuggestedBranchLink": null, "id": "KluwvFPvKCxusUOmSQG4q", "name": "main", "git": null, "createdAt": "2025-06-16T00:30:26.760Z", "archivedAt": null, "archivedBy": null, "headCommitId": "qNNz4p8JMipdRXk4579YJ", "isDefault": true, "deletedAt": null, "workingRootBlockId": "a8Oul5Re6jsffvG4Ab5XZ"}}